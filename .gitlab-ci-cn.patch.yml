spec:
  inputs:
    deploy_aws_dev_cn:
      default: true
      description: Whether to enable the deploy_aws_dev_cn job
      type: boolean
    deploy_aws_preprod_cn:
      default: true
      description: Whether to enable the deploy_aws_preprod_cn job
      type: boolean
    deploy_aws_prod_cn:
      default: true
      description: Whether to enable the deploy_aws_prod_cn job
      type: boolean
---

.variables_cn: &variables_cn
    ECR_REGION: cn-northwest-1
    CN_ECR_REGISTRY: "068946021388.dkr.ecr.cn-northwest-1.amazonaws.com.cn"
    npm_config_registry: "https://registry.npmmirror.com"
    TRIVY_DB_REPOSITORY: "${CN_ECR_REGISTRY}/ghcr.io/aquasecurity/trivy-db:2"
    TRIVY_JAVA_DB_REPOSITORY: "${CN_ECR_REGISTRY}/ghcr.io/aquasecurity/trivy-java-db:1"
    TRIVY_TIMEOUT: "15m"

variables:
  CN_DEV_ENVIRONMENT: mobile-apps-backend-cn-dev
  CN_PREPROD_ENVIRONMENT: "mobile-apps-backend-cn-preprod"
  CN_PROD_ENVIRONMENT: "mobile-apps-backend-cn-prod"
  CN_DEV_PIPELINE_ROLE: "arn:aws-cn:iam::047063831595:role/cdk-pipeline-role"
  CN_PREPROD_PIPELINE_ROLE: "arn:aws-cn:iam::047071156672:role/cdk-pipeline-role"
  CN_PROD_PIPELINE_ROLE: "arn:aws-cn:iam::047076489337:role/cdk-pipeline-role"
  CN_VCDP_MULTITENANT_ACCEPTABLE_VALUES: ""

.switch_pipeline_role: &switch_pipeline_role
  - aws_credentials=$(aws sts assume-role --role-arn "${PIPELINE_ROLE_CN}" --role-session-name  java-cdk-ci --duration-seconds 3600 --output json)
  - export AWS_ACCESS_KEY_ID=$(echo "$aws_credentials"| sed 's/[{"\|,]//g' | grep AccessKeyId | awk -F ':' '{print $2}' | sed 's/^[ ]*//')
  - export AWS_SECRET_ACCESS_KEY=$(echo "$aws_credentials"| sed 's/[{"\|,]//g' | grep SecretAccessKey | awk -F ':' '{print $2}' | sed 's/^[ ]*//')
  - export AWS_SESSION_TOKEN=$(echo "$aws_credentials"| sed 's/[{"\|,]//g' | grep SessionToken | awk -F ':' '{print $2}' | sed 's/^[ ]*//')

cdk_synth_cn:
  extends: cdk_synth
  tags:
    - mobile-apps-backend-cn-dev
  environment:
    name: $CN_DEV_ENVIRONMENT
  variables:
    <<: *variables_cn
    PIPELINE_ROLE_CN: $CN_DEV_PIPELINE_ROLE
    VCDP_MULTITENANT_ACCEPTABLE_VALUES: $CN_VCDP_MULTITENANT_ACCEPTABLE_VALUES
  image: "$CN_ECR_REGISTRY/$CUSTOM_CDK_IMAGE"
  before_script:
    - export MAVEN_ARGS="--gs $MAVEN_SETTINGS_PATH_CN -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
    - apk update && apk add --no-cache aws-cli
    # - npm config set registry https://registry.npmmirror.com
    - npm i -g esbuild
    - !reference [ .install_newest_aws_cdk, before_script ]
    - *switch_pipeline_role
  script:
    - cd infrastructure
    - cdk synth --output cdk.out.cn
  cache: # to speed this and deploy_cn jobs
    - key: "${CI_COMMIT_REF_SLUG}-infra"
      paths:
        - ".m2/repository"
  artifacts:
    expire_in: 15min
    paths:
      - "**/target/"
      - infrastructure/cdk.out/

# use local ECR image, mirrors of mvn repoistory and npm registry for network issues
.deploy_cn:
  extends: .deploy
  dependencies:
    - cdk_synth
    - cdk_synth_cn
  variables:
    <<: *variables_cn
  image: "$CN_ECR_REGISTRY/$CUSTOM_CDK_IMAGE"
  before_script:
    - export MAVEN_ARGS="--gs $MAVEN_SETTINGS_PATH_CN -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
    - apk update && apk add --no-cache aws-cli
    # - npm config set registry https://registry.npmmirror.com
    - npm i -g esbuild
    - !reference [ .install_newest_aws_cdk, before_script ]
    - echo "Pipeline role is ${PIPELINE_ROLE_CN}, AWS_ROLE_ARN is ${AWS_ROLE_ARN}"
    - *switch_pipeline_role

deploy_aws_dev_cn:
  extends: .deploy_cn
  stage: dev
  tags:
    - mobile-apps-backend-cn-dev
  environment:
    name: $CN_DEV_ENVIRONMENT
  variables:
    PIPELINE_ROLE_CN: $CN_DEV_PIPELINE_ROLE
    VCDP_MULTITENANT_ACCEPTABLE_VALUES: $CN_VCDP_MULTITENANT_ACCEPTABLE_VALUES
  cache:
    - key: "${CI_COMMIT_REF_SLUG}-infra"
      paths:
        - ".m2/repository"
      policy: pull
  rules:
    - if: '"$[[ inputs.deploy_aws_dev_cn ]]" == "false"'
      when: never
    - !reference [deploy_aws_dev, rules]

deploy_aws_preprod_cn:
  extends: .deploy_cn
  stage: preprod
  needs:
    - job: cdk_synth
    - job: cdk_synth_cn
    - job: deploy_aws_dev_cn
  tags:
    - mobile-apps-backend-cn-preprod
  environment:
    name: $CN_PREPROD_ENVIRONMENT
  variables:
    PIPELINE_ROLE_CN: $CN_PREPROD_PIPELINE_ROLE
    VCDP_MULTITENANT_ACCEPTABLE_VALUES: $CN_VCDP_MULTITENANT_ACCEPTABLE_VALUES
  rules:
    - if: '"$[[ inputs.deploy_aws_preprod_cn ]]" == "false"'
      when: never
    - !reference [deploy_aws_preprod, rules]

# deploy_aws_prod_cn for cn
deploy_aws_prod_cn:
  extends: .deploy_cn
  stage: production
  needs:
    - job: cdk_synth
    - job: cdk_synth_cn
  tags:
    - mobile-apps-backend-cn-prod
  environment:
    name: $CN_PROD_ENVIRONMENT
  variables:
    PIPELINE_ROLE_CN: $CN_PROD_PIPELINE_ROLE
    VCDP_MULTITENANT_ACCEPTABLE_VALUES: $CN_VCDP_MULTITENANT_ACCEPTABLE_VALUES
  rules:
    - if: '"$[[ inputs.deploy_aws_prod_cn ]]" == "false"'
      when: never
    - !reference [deploy_aws_prod, rules]
