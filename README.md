# OneApp Backend CDK Java project

This is a project for CDK development and deploy OAB resources, which could be separated into different stacks.

It is a [Maven](https://maven.apache.org/) based project, so you can open this project with any Maven compatible Java IDE to build and run
tests.

## Project structure:

* `src/main/java`  contains Java source files
* `src/test/java`  contains test files
* `cdk.out`        contains synthesized CloudFormation template
* `cdk.json`       config for the CDK Toolkit how to execute your app

## Project schema:

    oab-cdk-stacks/
    │
    ├── infrastructure/
    │   ├── src/
    │   │   ├── main/
    │   │   │   └── java/
    │   │   │       └── com...oneappbackend/
    │   │   │           ├── stacks/
    │   │   │           │   └── UserPreferencesDBStack.java
    │   │   │           └── InfrastructureApp.java
    │   │   └── test/
    │   │        └── java/
    │   │           └── com...oneappbackend/
    │   │               └── InfrastructureAppTest.java
    │   └── cdk.json
    │
    ├── scripts/
    │   └── insert_example_user_preferences.py
    │
    └── README.md

## Useful commands

In order to use the following cdk commands you need to have AWS CDK CLI installed:

```
npm install -g aws-cdk
```

Run the following command to verify a successful installation. The AWS CDK CLI should output the version number:

```
cdk --version
```

| Command     | Description                                          |
|-------------|------------------------------------------------------|
| mvn package | compile and run tests                                |
| cdk ls      | list all stacks in the app                           |
| cdk synth   | emits the synthesized CloudFormation template        |
| cdk deploy  | deploy this stack to your default AWS account/region |
| cdk diff    | compare deployed stack with current state            |
| cdk docs    | open CDK documentation                               |

## Testing

To run infrastructure code tests locally:


#### Create env VCDP_MULTITENANT_ACCEPTABLE_VALUES with following instructions: 
```
To update only the original tables, env must be empty e.g. ""
To update only the tenanted tables, env must only have comma separated tenantIds in it e.g. "jlr" or "jlr, tenant2"
To update original tables and tenanted tables, the env variable must have comma at the end along with comma separated tenantIds e.g. "jlr," or "jlr,tenant2,"
```

```
export VCDP_MULTITENANT_ACCEPTABLE_VALUES="jlr"
cd infrastructure
mvn test
```

## Verification

Run the below command to verify the project.
This will run all the tests, skipping the slow dependency checking, generate a report file for code coverage and check code style.

```
mvn verify -D dependency-check.skip=true
```

To see the code coverage report open `target/site/jacoco/index.html` on your web browser.

## Run these steps to test before commit

This section lists the steps required before committing to GitLab to reduce chances of pipeline failure.

#### Create env VCDP_MULTITENANT_ACCEPTABLE_VALUES with following instructions:
```
To update only the original tables, env must be empty e.g. ""
To update only the tenanted tables, env must only have comma separated tenantIds in it e.g. "jlr" or "jlr, tenant2"
To update original tables and tenanted tables, the env variable must have comma at the end along with comma separated tenantIds e.g. "jlr," or "jlr,tenant2,"
```

1. Run infrastructure code tests
    ```
    export VCDP_MULTITENANT_ACCEPTABLE_VALUES="jlr" 
    cd ../infrastructure
    mvn test
    ```
2. Generate Cloudformation template:
    ```
    cdk synth
    ```
3. Analyze infrastructure as code (IaC) scan results.
    ```
    checkov --directory . --quiet
    ```
4. Scan for vulnerabilities using Trivy locally.
    ```
    cd ..
    trivy fs . --scanners vuln,secret,config
    ```
5. Scan for vulnerabilities using Snyk locally.
    ```
    snyk test --maven-aggregate-project
    ```

## Deployment

To deploy to AWS directly (i.e. without pipeline),

1. Generate CloudFormation template.
    ```
    cd infrastructure
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers cdk synth
    ```

3. Deploy.
    ```
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers cdk deploy
    ```

   For more than one stack,
    ```
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers cdk deploy --all
    ```

## DynamoDB commands

To get information about DynamoDB Table - you should have access to AWS account and AWS CLI installed, run:

```
aws dynamodb describe-table --table-name UserPreferencesDev
```

To insert example user preferences data to the UserPreferencesDev table run the following commands:

```
cd scripts
pip install boto3
python insert_example_user_preferences.py
```

To check the items in the table run:

```
aws dynamodb scan --table-name UserPreferencesDev
```

## License and copyright

Copyright (c) 2023. Jaguar Land Rover - All Rights Reserved.

CONFIDENTIAL INFORMATION - DO NOT DISTRIBUTE
