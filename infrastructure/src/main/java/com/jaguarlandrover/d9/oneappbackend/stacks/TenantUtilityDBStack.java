/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.stacks;

import software.amazon.awscdk.RemovalPolicy;
import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.dynamodb.Attribute;
import software.amazon.awscdk.services.dynamodb.AttributeType;
import software.amazon.awscdk.services.dynamodb.BillingMode;
import software.amazon.awscdk.services.dynamodb.PointInTimeRecoverySpecification;
import software.amazon.awscdk.services.dynamodb.Table;
import software.amazon.awscdk.services.dynamodb.TableEncryption;
import software.constructs.Construct;

public class TenantUtilityDBStack extends Stack {

  public TenantUtilityDBStack(final Construct scope, String stackId, final StackProps props) {
    super(scope, stackId, props);
  }

  public Table.Builder buildTenantedTable(String tableName, final String tableId, boolean pitr) {
    return Table.Builder.create(this, tableName)
        .tableName(tableName)
        .partitionKey(Attribute.builder()
            .name(tableId)
            .type(AttributeType.STRING)
            .build())
        .pointInTimeRecoverySpecification(PointInTimeRecoverySpecification.builder()
            .pointInTimeRecoveryEnabled(pitr)  // Point-In-Time Recovery (PITR)
            .build())
        .billingMode(BillingMode.PROVISIONED)
        .readCapacity(5)
        .writeCapacity(1)
        .encryption(TableEncryption.AWS_MANAGED)
        .removalPolicy(RemovalPolicy.RETAIN)  // Prevents deletion of the table
        .deletionProtection(true);  // Enables delete protection
  }
}
