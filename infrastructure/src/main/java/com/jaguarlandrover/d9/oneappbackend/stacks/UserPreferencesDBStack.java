/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.stacks;

import software.amazon.awscdk.StackProps;
import software.constructs.Construct;

public class UserPreferencesDBStack extends TenantUtilityDBStack {

  public static final String STACK_ID = "UserPreferencesStack";
  private static final String TABLE_ID = "UserId";

  public UserPreferencesDBStack(final Construct scope,
                                final StackProps props,
                                String tableName,
                                String stackId,
                                boolean pitr) {

    super(scope, stackId, props);

    buildTenantedTable(tableName, TABLE_ID, pitr).build();
  }
}
