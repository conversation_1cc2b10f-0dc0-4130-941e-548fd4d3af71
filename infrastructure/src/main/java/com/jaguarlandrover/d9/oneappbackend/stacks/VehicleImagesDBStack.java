/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.stacks;

import software.amazon.awscdk.StackProps;
import software.constructs.Construct;

public class VehicleImagesDBStack extends TenantUtilityDBStack {

  public static final String STACK_ID = "VehicleImagesStack";
  public static final String TABLE_ID = "OrderId";
  public static final String TIME_TO_LIVE_ATTRIBUTE = "TimeToLive";

  public VehicleImagesDBStack(final Construct scope,
                              final StackProps props,
                              String tableName,
                              String stackId,
                              boolean pitr) {

    super(scope, stackId, props);

    buildTenantedTable(tableName, TABLE_ID, pitr)
        .timeToLiveAttribute(TIME_TO_LIVE_ATTRIBUTE)
        .build();
  }
}
