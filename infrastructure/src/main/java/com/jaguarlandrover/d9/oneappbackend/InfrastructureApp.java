/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.jaguarlandrover.d9.oneappbackend.config.OabInfrastructureConfig;
import com.jaguarlandrover.d9.oneappbackend.stacks.UserPreferencesDBStack;
import com.jaguarlandrover.d9.oneappbackend.stacks.VehicleConsentsDBStack;
import com.jaguarlandrover.d9.oneappbackend.stacks.VehicleImagesDBStack;
import java.io.File;
import java.io.IOException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Environment;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.Tags;

public class InfrastructureApp {

  private static final String DEFAULT_CONFIG_ENV = "mobile-apps-backend-developers";

  public static final String VCDP_MULTITENANT_ACCEPTABLE_VALUES = "VCDP_MULTITENANT_ACCEPTABLE_VALUES";

  private static final Logger LOGGER = LogManager.getLogger(InfrastructureApp.class);

  private static String[] tenants;

  public static void main(final String[] args) {
    if (System.getenv(VCDP_MULTITENANT_ACCEPTABLE_VALUES) == null) {
      LOGGER.error("Tenant list is empty, please populate environment variable {}", VCDP_MULTITENANT_ACCEPTABLE_VALUES);
      System.exit(1);
    } else {
      tenants = System.getenv(VCDP_MULTITENANT_ACCEPTABLE_VALUES).split(",", -1);
    }

    App app = new App();

    final String configEnv = System.getenv("CI_ENVIRONMENT_NAME");

    // Point-In-Time Recovery (PITR)
    boolean pitr = true;
    // Application Configurations from YML file
    OabInfrastructureConfig config = loadConfiguration(configEnv);

    Environment env = Environment.builder()
        .account(config.getAccount())
        .region(config.getRegion())
        .build();

    for (String tenantId : tenants) {
      new UserPreferencesDBStack(app, StackProps.builder().env(env).build(),
          prefixAwsTable(config.getUserPreferencesTable(), tenantId), prefixAwsStack(UserPreferencesDBStack.STACK_ID, tenantId), pitr);
      new VehicleConsentsDBStack(app, StackProps.builder().env(env).build(),
          prefixAwsTable(config.getVehicleConsentsTable(), tenantId), prefixAwsStack(VehicleConsentsDBStack.STACK_ID, tenantId), pitr);
      new VehicleImagesDBStack(app, StackProps.builder().env(env).build(), prefixAwsTable(config.getVehicleImagesTable(), tenantId),
          prefixAwsStack(VehicleImagesDBStack.STACK_ID, tenantId), pitr);
    }

    Tags.of(app).add("service", "One App Backend");
    Tags.of(app).add("version", "0.0.0");
    Tags.of(app).add("managed_by", "OAB Pineapple");
    Tags.of(app).add("pii_data_handler", "false");
    Tags.of(app).add("squad", "Pineapple");

    app.synth();
  }

  public static OabInfrastructureConfig loadConfiguration(String configEnv) {
    if (configEnv == null || configEnv.isEmpty()) {
      configEnv = DEFAULT_CONFIG_ENV;
    }

    File file = new File(Thread.currentThread().getContextClassLoader()
        .getResource(String.format("config/%s.yaml", configEnv)).getFile());

    // Instantiating a new ObjectMapper as a YAMLFactory
    ObjectMapper objectMapper = new ObjectMapper(new YAMLFactory());

    OabInfrastructureConfig oabInfrastructureConfig;
    try {
      LOGGER.info("Configuration file read.");
      oabInfrastructureConfig = objectMapper.readValue(file, OabInfrastructureConfig.class);
    } catch (IOException e) {
      LOGGER.error(e.getMessage(), e);
      oabInfrastructureConfig = new OabInfrastructureConfig();
    }
    return oabInfrastructureConfig;
  }

  public static String prefixAwsTable(String objectName, String tenantId) {
    if (!tenantId.isEmpty()) {
      objectName = tenantId + "_" + objectName;
    }
    return objectName;
  }

  public static String prefixAwsStack(String objectName, String tenantId) {
    if (!tenantId.isEmpty()) {
      objectName = tenantId + objectName;
    }
    return objectName;
  }


}
