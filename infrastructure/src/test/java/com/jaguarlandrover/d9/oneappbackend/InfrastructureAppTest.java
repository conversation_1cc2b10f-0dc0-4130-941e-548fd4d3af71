/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend;

import static com.jaguarlandrover.d9.oneappbackend.InfrastructureApp.loadConfiguration;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.jaguarlandrover.d9.oneappbackend.config.OabInfrastructureConfig;
import com.jaguarlandrover.d9.oneappbackend.stacks.UserPreferencesDBStack;
import com.jaguarlandrover.d9.oneappbackend.stacks.VehicleConsentsDBStack;
import com.jaguarlandrover.d9.oneappbackend.stacks.VehicleImagesDBStack;
import java.util.Map;
import org.junit.jupiter.api.Test;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Environment;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.assertions.Template;


class InfrastructureAppTest {

  String[] tenants = System.getenv(InfrastructureApp.VCDP_MULTITENANT_ACCEPTABLE_VALUES).split(",", -1);
  OabInfrastructureConfig infrastructureConfig = loadConfiguration(null);

  @Test
  void testUserPreferencesStack() {

    // Point-In-Time Recovery (PITR)
    boolean pitr = true;

    for (String tenantId : tenants) {
      App app = new App();
      UserPreferencesDBStack stack = new UserPreferencesDBStack(app, StackProps.builder()
          .env(Environment.builder()
              .account("account-id")
              .region("region")
              .build())
          .build(),
          InfrastructureApp.prefixAwsTable(infrastructureConfig.getUserPreferencesTable(), tenantId),
          InfrastructureApp.prefixAwsStack(UserPreferencesDBStack.STACK_ID, tenantId),
          pitr);

      Template template = Template.fromStack(stack);

      // Ensure that the template is not null
      assertNotNull(template, "Template should not be null");

      template.hasResourceProperties("AWS::DynamoDB::Table", Map.of(
          "TableName", InfrastructureApp.prefixAwsTable(infrastructureConfig.getUserPreferencesTable(), tenantId)));

    }
  }

  @Test
  void testVehicleConsentsStack() {
    // Point-In-Time Recovery (PITR)
    boolean pitr = true;

    for (String tenantId : tenants) {
      App app = new App();

      VehicleConsentsDBStack stack = new VehicleConsentsDBStack(app, StackProps.builder()
          .env(Environment.builder()
              .account("account-id")
              .region("region")
              .build())
          .build(),
          InfrastructureApp.prefixAwsTable(infrastructureConfig.getVehicleConsentsTable(), tenantId),
          InfrastructureApp.prefixAwsStack(VehicleConsentsDBStack.STACK_ID, tenantId),
          pitr);

      Template template = Template.fromStack(stack);

      // Ensure that the template is not null
      assertNotNull(template, "Template should not be null");

      template.hasResourceProperties("AWS::DynamoDB::Table", Map.of(
          "TableName", InfrastructureApp.prefixAwsTable(infrastructureConfig.getVehicleConsentsTable(), tenantId)));
    }
  }

  @Test
  void testVehicleImagesStack() {

    // Point-In-Time Recovery (PITR)
    boolean pitr = true;
    for (String tenantId : tenants) {
      App app = new App();

      VehicleImagesDBStack stack = new VehicleImagesDBStack(app, StackProps.builder()
          .env(Environment.builder()
              .account("account-id")
              .region("region")
              .build())
          .build(),
          InfrastructureApp.prefixAwsTable(infrastructureConfig.getVehicleImagesTable(), tenantId),
          InfrastructureApp.prefixAwsStack(VehicleImagesDBStack.STACK_ID, tenantId),
          pitr);

      Template template = Template.fromStack(stack);

      // Ensure that the template is not null
      assertNotNull(template, "Template should not be null");

      template.hasResourceProperties("AWS::DynamoDB::Table", Map.of(
          "TableName", InfrastructureApp.prefixAwsTable(infrastructureConfig.getVehicleImagesTable(), tenantId)));
    }
  }
}
