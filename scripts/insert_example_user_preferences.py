#  Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved

import boto3
import json

try:
    client = boto3.resource('dynamodb', region_name="eu-west-2")
    table = client.Table('UserPreferencesDev')

    file = open('example_user_preferences.json')
    data = json.load(file)

    for item in data:
        preferences = {k: v for k, v in item.items() if k != 'UserId'}
        print("UserId: {}, Preferences: {}".format(item["UserId"], preferences))

        table.put_item(
            Item=item
        )

except Exception as e:
    print(e)
