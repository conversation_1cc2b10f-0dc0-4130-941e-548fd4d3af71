include:
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/security/base-sec.yml"
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/security/java-sec.yml"
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/security/snyk-target-reference.yml"
  - local: ".gitlab-ci-cn.patch.yml"

stages:
  - build
  - validate
  - inspect code
  - test
  - dev
  - dev test
  - preprod
  - preprod test
  - production
  - production test

variables:
  DEV_ENVIRONMENT: mobile-apps-backend-developers
  PRE_PROD_ENVIRONMENT: mobile-apps-backend-pre-production
  PROD_ENVIRONMENT: mobile-apps-backend-production
  DEV_ENV_TAG: mab-developers
  PREPROD_ENV_TAG: mab-pre-production
  PROD_ENV_TAG: mab-production
  ECR_REGION: eu-west-2
  BASE_JAVA_JOB_IMAGE: maven:3.8.1-openjdk-17-slim
  CUSTOM_CDK_IMAGE: "557068259488.dkr.ecr.eu-west-2.amazonaws.com/java-17-aws-cdk"
  SONAR_PROJECT_KEY: "gitlab-project-id-$CI_PROJECT_ID"
  SNYK_UPLOAD_IMAGE: snyk/snyk:maven
  VCDP_MULTITENANT_ACCEPTABLE_VALUES: "jlr,"

.install_newest_aws_cdk: &install_newest_aws_cdk
  before_script:
    - npm install -g aws-cdk

.dev-runners: &dev-runners
  tags:
    - aws
    - $DEV_ENV_TAG

snyk:
  <<: *dev-runners
snyk-code:
  <<: *dev-runners
trivy:
  <<: *dev-runners
semgrep:
  <<: *dev-runners

# Overriding from snyk-target-reference.yml
.snyk-env-upload:
  image:
    name: snyk/snyk:maven
    entrypoint: [""]
  <<: *dev-runners

# Remove when ready for preprod
snyk-preprod-upload:
  rules:
    - when: never

# Remove when ready for prod
snyk-production-upload:
  rules:
    - when: never

.sonar_on_mr_rules:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" # Add the job to merge request pipelines if there's an open merge request.
    - if: $CI_OPEN_MERGE_REQUESTS # Don't add it to a *branch* pipeline if it's already in a merge request pipeline.
      when: never
    - if: $CI_COMMIT_BRANCH # If there's no open merge request, add it to a *branch* pipeline instead.

cdk_synth:
  <<: *dev-runners
  stage: build
  image: $CUSTOM_CDK_IMAGE
  extends: .install_newest_aws_cdk
  script:
    - export ACCOUNT_ID=$(echo $AWS_ROLE_ARN | awk -F ':' '{print $5}')
    - cd infrastructure
    - cdk synth
  environment:
    name: $DEV_ENVIRONMENT
  variables:
    REGION: $ECR_REGION
    DEPLOYMENT_ENV: $CI_JOB_STAGE

  rules:
    - !reference [ .sonar_on_mr_rules, rules ]
  artifacts:
    expire_in: 15min
    paths:
      - "**/target/"
      - infrastructure/cdk.out/

cdk_diff:
  extends: .install_newest_aws_cdk
  stage: validate
  image: $CUSTOM_CDK_IMAGE
  needs:
    - cdk_synth
  <<: *dev-runners
  script:
    - cd infrastructure
    - cdk diff
  rules:
    - !reference [ .sonar_on_mr_rules, rules ]

cdk_doctor:
  <<: *dev-runners
  stage: inspect code
  image: $CUSTOM_CDK_IMAGE
  needs:
    - cdk_synth
  extends: .install_newest_aws_cdk
  script:
    - cd infrastructure
    - cdk doctor --verbose
  environment:
    name: $DEV_ENVIRONMENT
  rules:
    - !reference [ .sonar_on_mr_rules, rules ]

checkov:
  <<: *dev-runners
  stage: inspect code
  image:
    name: bridgecrew/checkov
    entrypoint:
      - /usr/bin/env
  script:
    - cd infrastructure
    - checkov --directory "$CI_PROJECT_DIR" --hard-fail MEDIUM
  needs:
    - cdk_synth
  rules:
    - !reference [.sonar_on_mr_rules, rules]

sonarqube:
  <<: *dev-runners
  stage: inspect code
  image: $BASE_JAVA_JOB_IMAGE
  before_script:
    - mvn test -f infrastructure/pom.xml jacoco:report
  script:
    # SONAR_HOST_URL and SONAR_PROJECT_KEY are defined in base-sec.yml
    - mvn -f infrastructure/ $MAVEN_CLI_OPTS sonar:sonar -Dsonar.projectKey="$SONAR_PROJECT_KEY" -Dsonar.token="$SONAR_TOKEN" -Dsonar.qualitygate.wait=true -Dcheckstyle.skip -Ddependency-check.skip=true --gs $MAVEN_SETTINGS_PATH -Dsonar.coverage.jacoco.xmlReportPaths=./target/site/jacoco/jacoco.xml
  allow_failure: true

test_infrastructure:
  <<: *dev-runners
  stage: test
  image: $CUSTOM_CDK_IMAGE
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  script:
    - cd infrastructure/
    - mvn $MAVEN_CLI_OPTS test
  rules:
    - !reference [ .sonar_on_mr_rules, rules ]

.deploy:
  <<: *dev-runners
  image: $CUSTOM_CDK_IMAGE
  extends: .install_newest_aws_cdk
  script:
    - export ACCOUNT_ID=$(echo $AWS_ROLE_ARN | awk -F ':' '{print $5}')
    - cd infrastructure
    - cdk bootstrap aws://${ACCOUNT_ID}/$ECR_REGION
    - cdk deploy --all --require-approval=never

deploy_aws_dev:
  <<: *dev-runners
  extends: .deploy
  stage: dev
  variables:
    REGION: $ECR_REGION
    DEPLOYMENT_ENV: $CI_JOB_STAGE
  environment:
    name: $DEV_ENVIRONMENT
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
      when: on_success

deploy_aws_preprod:
  tags:
    - aws
    - $PREPROD_ENV_TAG
  extends: .deploy
  stage: preprod
  variables:
    REGION: $ECR_REGION
    DEPLOYMENT_ENV: $CI_JOB_STAGE
  environment:
    name: $PRE_PROD_ENVIRONMENT
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
      when: manual

deploy_aws_prod:
  tags:
    - aws
    - $PROD_ENV_TAG
  extends: .deploy
  stage: production
  variables:
    REGION: $ECR_REGION
    DEPLOYMENT_ENV: $CI_JOB_STAGE
  environment:
    name: $PROD_ENVIRONMENT
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
      when: manual
